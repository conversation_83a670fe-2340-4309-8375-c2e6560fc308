{"name": "@kdt-farm/shredstream-decoder", "type": "module", "version": "0.0.0", "packageManager": "pnpm@10.11.1", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/kdt-farm/shredstream-decoder", "repository": "github:kdt-farm/shredstream-decoder", "bugs": {"email": "<EMAIL>", "url": "https://github.com/kdt-farm/shredstream-decoder/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/types/index.d.ts", "require": "./dist/index.cjs", "default": "./dist/index.js"}}, "main": "dist/index.js", "types": "dist/types/index.d.ts", "files": ["dist"], "scripts": {"build": "rimraf dist && tsup && tsc --project ./tsconfig.build.json", "release": "tsx scripts/release.ts && changelogen gh release && pnpm publish", "up": "ncu -i", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "preinstall": "npx only-allow pnpm", "prepare": "simple-git-hooks", "prepublishOnly": "pnpm build"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@kdt-farm/eslint-config": "^0.0.2", "@kdt310722/tsconfig": "^1.0.0", "@swc/core": "^1.11.29", "@types/node": "^22.15.29", "changelogen": "^0.6.1", "eslint": "^9.28.0", "execa": "^9.6.0", "lint-staged": "^16.1.0", "npm-check-updates": "^18.0.1", "only-allow": "^1.2.1", "rimraf": "^6.0.1", "simple-git-hooks": "^2.13.0", "tsup": "^8.5.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "publishConfig": {"access": "public"}, "commitlint": {"extends": "@commitlint/config-conventional"}, "simple-git-hooks": {"commit-msg": "npx --no -- commitlint --edit ${1}", "pre-commit": "npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}}