# Shredstream Decoder

A simple Node.js/TypeScript library that provides a single function to decode Solana entries from Jito Shredstream data using WebAssembly (WASM).

## Overview

This library provides **one function only** to decode serialized Solana entries received from Jito Shredstream's `SubscribeEntries` RPC call. It uses Rust + WASM with `bincode` and crates from Solana like `solana_entry`, `solana_transaction`, `solana_signature`, `solana_message`, etc. for high-performance deserialization.

### What it does

Takes the protobuf `Entry` message you receive from Jito Shredstream and decodes the serialized `entries` bytes into an array of Solana VersionedTransaction structs with some additional fields from Solana Entry struct.

**Input**: Protobuf Entry message from Jito Shredstream

```typescript
{
  slot: bigint,
  entries: Buffer  // Serialized bytes of Vec<Entry>
}
```

**Output**: Decoded entries with TypeScript types

```typescript
[
    // Array of decoded Solana VersionedTransaction structs with `slot` field
    {
        slot: bigint,
        signatures: <PERSON><PERSON><PERSON>[], // Array of decoded Solana Signature structs
        message: VersionedMessage // Decoded Solana VersionedMessage struct
    },
]
```

## API

### Single Function

```typescript
import { decodeEntries } from '@kdt-farm/shredstream-decoder'

// Decode entries from Jito Shredstream protobuf message
const result = decodeEntries({
    slot: 123456789n,
    entries: buffer, // Buffer containing serialized Vec<Entry>
})

console.log(result[0].slot) // 123456789n
console.log(result[0].message) // VersionedMessage - decoded Solana VersionedMessage struct
```

## TypeScript Types

```typescript
interface VersionedTransaction {
    slot: bigint

    // Solana transaction structure
    // (detailed types will be provided in the library)
}

interface Entry {
    slot: bigint
    entries: Buffer
}

// Main function signature
declare function decodeEntries(input: DecodeEntriesInput): DecodeEntriesOutput
```

## References

### Official Documentation

- [Jito Shredstream Documentation](https://docs.jito.wtf/lowlatencytxnfeed/)
- [Solana Entry Struct Documentation](https://docs.rs/solana-entry/latest/solana_entry/entry/struct.Entry.html)

### Protocol Buffer Definitions

- [Shared Proto](https://raw.githubusercontent.com/jito-labs/mev-protos/c9614089ef48fb83f01767d87e8f73e6c2e59c0b/shared.proto)
- [Shredstream Proto](https://raw.githubusercontent.com/jito-labs/mev-protos/c9614089ef48fb83f01767d87e8f73e6c2e59c0b/shredstream.proto)

### Example Implementation

- [Rust Deshred Example](https://raw.githubusercontent.com/jito-labs/shredstream-proxy/refs/heads/master/examples/deshred.rs)

### Related Projects

- [Jito Shredstream Proxy](https://github.com/jito-labs/shredstream-proxy)
- [Jito MEV Protos](https://github.com/jito-labs/mev-protos)
