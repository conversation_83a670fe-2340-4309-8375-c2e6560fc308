[package]
name = "shredstream-decoder"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
# Solana crates
solana-entry = "2.2.7"
solana-transaction = "2.2.2"

# Serialization
bincode = "1.3"
serde = { version = "1.0", features = ["derive"] }

# WASM bindings
wasm-bindgen = "0.2"
js-sys = "0.3"

[dependencies.getrandom]
version = "0.2"
features = ["js"]

# Replace getrandom v0.3 with v0.2 for WASM compatibility
[replace]
"getrandom:0.3.3" = { version = "0.2", features = ["js"] }

[profile.release]
opt-level = "s"
lto = true
