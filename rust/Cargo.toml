[package]
name = "shredstream-decoder"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
# Serialization
bincode = "1.3"
serde = { version = "1.0", features = ["derive"] }

# WASM bindings
wasm-bindgen = "0.2"
js-sys = "0.3"

# Console logging for debugging
console_error_panic_hook = "0.1"
web-sys = { version = "0.3", features = ["console"] }

# Crypto for signatures
ed25519-dalek = { version = "1.0", default-features = false, features = [
    "u64_backend",
] }
sha2 = "0.10"

[dependencies.getrandom]
version = "0.2"
features = ["js"]

[profile.release]
opt-level = "s"
lto = true
