{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 8958406094080315647, "profile": 155187846257117564, "path": 2667334379050259302, "deps": [[1988483478007900009, "unicode_ident", false, 11801641621428967004], [14299170049494554845, "build_script_build", false, 16847458195336142335]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wasm-bindgen-shared-e17c93344b0c3c41/dep-lib-wasm_bindgen_shared", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}