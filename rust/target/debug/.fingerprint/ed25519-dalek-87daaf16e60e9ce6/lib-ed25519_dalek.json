{"rustc": 15497389221046826682, "features": "[\"u64_backend\"]", "declared_features": "[\"alloc\", \"asm\", \"batch\", \"batch_deterministic\", \"default\", \"legacy_compatibility\", \"merlin\", \"nightly\", \"rand\", \"rand_core\", \"serde\", \"serde_bytes\", \"serde_crate\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 16409354033026609460, "profile": 8276155916380437441, "path": 17749628708327961150, "deps": [[2932480923465029663, "zeroize", false, 4166717787507452159], [9431183304631869056, "curve25519_dalek", false, 6308737697418655184], [11472355562936271783, "sha2", false, 7931037531281538649], [16629266738323756185, "ed25519", false, 12252393150166970022]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ed25519-dalek-87daaf16e60e9ce6/dep-lib-ed25519_dalek", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}