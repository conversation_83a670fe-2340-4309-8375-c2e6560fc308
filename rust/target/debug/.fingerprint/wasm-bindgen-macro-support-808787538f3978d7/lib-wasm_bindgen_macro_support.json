{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 17930477452216118438, "profile": 155187846257117564, "path": 10215168194706528797, "deps": [[3060637413840920116, "proc_macro2", false, 9689789723025982531], [14299170049494554845, "wasm_bindgen_shared", false, 16206105308725806329], [14372503175394433084, "wasm_bindgen_backend", false, 3006980210237165424], [17990358020177143287, "quote", false, 16463695800006207511], [18149961000318489080, "syn", false, 8548565062926475254]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wasm-bindgen-macro-support-808787538f3978d7/dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}