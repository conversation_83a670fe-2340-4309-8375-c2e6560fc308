{"rustc": 15497389221046826682, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 1331003669356655947, "path": 1545970865074875010, "deps": [[3722963349756955755, "once_cell", false, 6406788738401304248], [6946689283190175495, "build_script_build", false, 14101174280776793749], [7858942147296547339, "rustversion", false, 2277225609138854432], [10411997081178400487, "cfg_if", false, 10210376222278810431], [11382113702854245495, "wasm_bindgen_macro", false, 17068843087829439753]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wasm-bindgen-3d4a26719e25417e/dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}