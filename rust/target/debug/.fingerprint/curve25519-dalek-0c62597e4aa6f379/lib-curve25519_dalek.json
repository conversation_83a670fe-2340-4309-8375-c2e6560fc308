{"rustc": 15497389221046826682, "features": "[\"u64_backend\"]", "declared_features": "[\"alloc\", \"avx2_backend\", \"default\", \"fiat-crypto\", \"fiat_u32_backend\", \"fiat_u64_backend\", \"nightly\", \"packed_simd\", \"serde\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 4744499769514376500, "profile": 8276155916380437441, "path": 3006486542479493288, "deps": [[1740877332521282793, "rand_core", false, 9645445430659043118], [2932480923465029663, "zeroize", false, 4166717787507452159], [3712811570531045576, "byteorder", false, 13390095773407350662], [6374421995994392543, "digest", false, 8000667850972141626], [17003143334332120809, "subtle", false, 6766196283229480921]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/curve25519-dalek-0c62597e4aa6f379/dep-lib-curve25519_dalek", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}