{"rustc": 15497389221046826682, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 13212958829184517904, "path": 1545970865074875010, "deps": [[3722963349756955755, "once_cell", false, 9237853476007942550], [6946689283190175495, "build_script_build", false, 16129262984520897608], [7858942147296547339, "rustversion", false, 4668403157449673101], [10411997081178400487, "cfg_if", false, 1172599417165741463], [11382113702854245495, "wasm_bindgen_macro", false, 1706102125612015984]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/wasm-bindgen-5f083792333245db/dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": ["--cfg", "getrandom_js", "--cfg", "wasm_js"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}