{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"default\", \"derive-preview\", \"digest\", \"digest-preview\", \"hazmat-preview\", \"rand-preview\", \"rand_core\", \"signature_derive\", \"std\"]", "target": 14677263450862682510, "profile": 10036544270487789663, "path": 12971614864629602869, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/signature-aef8709e67574072/dep-lib-signature", "checksum": false}}], "rustflags": ["--cfg", "getrandom_js", "--cfg", "wasm_js"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}