{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17001665395952474378, "build_script_build", false, 277611023036665131]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/typenum-873a88f5a09c646d/output", "paths": ["tests"]}}], "rustflags": ["--cfg", "getrandom_js", "--cfg", "wasm_js"], "config": 0, "compile_kind": 0}