{"rustc": 15497389221046826682, "features": "[\"u64_backend\"]", "declared_features": "[\"alloc\", \"avx2_backend\", \"default\", \"fiat-crypto\", \"fiat_u32_backend\", \"fiat_u64_backend\", \"nightly\", \"packed_simd\", \"serde\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 4744499769514376500, "profile": 10036544270487789663, "path": 3006486542479493288, "deps": [[1740877332521282793, "rand_core", false, 5606624559761273851], [2932480923465029663, "zeroize", false, 777685824808534953], [3712811570531045576, "byteorder", false, 15874876370982787420], [6374421995994392543, "digest", false, 262422508038501217], [17003143334332120809, "subtle", false, 18186076477627801520]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/curve25519-dalek-091ad46155f22c28/dep-lib-curve25519_dalek", "checksum": false}}], "rustflags": ["--cfg", "getrandom_js", "--cfg", "wasm_js"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}