{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 9676079782213560798, "profile": 10036544270487789663, "path": 10906107496444936055, "deps": [[6946689283190175495, "wasm_bindgen", false, 15172423662492620814], [10411997081178400487, "cfg_if", false, 1172599417165741463]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/console_error_panic_hook-85114b600cc2170a/dep-lib-console_error_panic_hook", "checksum": false}}], "rustflags": ["--cfg", "getrandom_js", "--cfg", "wasm_js"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}