{"rustc": 15497389221046826682, "features": "[\"js\", \"js-sys\", \"wasm-bindgen\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 10036544270487789663, "path": 13349453684238134133, "deps": [[6946689283190175495, "wasm_bindgen", false, 15172423662492620814], [9003359908906038687, "js_sys", false, 3474912498479159721], [10411997081178400487, "cfg_if", false, 1172599417165741463]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/getrandom-8009ce29adc6628f/dep-lib-getrandom", "checksum": false}}], "rustflags": ["--cfg", "getrandom_js", "--cfg", "wasm_js"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}