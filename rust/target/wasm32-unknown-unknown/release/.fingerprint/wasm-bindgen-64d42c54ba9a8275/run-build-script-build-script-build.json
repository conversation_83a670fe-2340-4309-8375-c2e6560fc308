{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[6946689283190175495, "build_script_build", false, 1471243866678878004]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/wasm-bindgen-64d42c54ba9a8275/output", "paths": ["build.rs"]}}], "rustflags": ["--cfg", "getrandom_js", "--cfg", "wasm_js"], "config": 0, "compile_kind": 0}