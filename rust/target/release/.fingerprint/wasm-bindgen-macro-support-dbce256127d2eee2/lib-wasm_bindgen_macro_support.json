{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 17930477452216118438, "profile": 2554159132106277380, "path": 10215168194706528797, "deps": [[3060637413840920116, "proc_macro2", false, 12996683187931920108], [14299170049494554845, "wasm_bindgen_shared", false, 10831964494616380931], [14372503175394433084, "wasm_bindgen_backend", false, 18269242724669188180], [17990358020177143287, "quote", false, 5809964562785604787], [18149961000318489080, "syn", false, 11210316041979544057]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wasm-bindgen-macro-support-dbce256127d2eee2/dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}