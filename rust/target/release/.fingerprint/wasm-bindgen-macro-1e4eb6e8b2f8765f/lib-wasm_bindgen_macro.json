{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 6875603382767429092, "profile": 2554159132106277380, "path": 10437665824755753453, "deps": [[2589611628054203282, "wasm_bindgen_macro_support", false, 2657090035914223796], [17990358020177143287, "quote", false, 5809964562785604787]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wasm-bindgen-macro-1e4eb6e8b2f8765f/dep-lib-wasm_bindgen_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}