{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 8958406094080315647, "profile": 2554159132106277380, "path": 2667334379050259302, "deps": [[1988483478007900009, "unicode_ident", false, 15232776345062601062], [14299170049494554845, "build_script_build", false, 1991318648434917785]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wasm-bindgen-shared-58889e2c1e414554/dep-lib-wasm_bindgen_shared", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}