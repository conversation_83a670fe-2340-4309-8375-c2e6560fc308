use crate::console_log;
use crate::types::{DecodedEntry, SolanaEntry};
use js_sys::Array;
use wasm_bindgen::prelude::*;

const MAX_INPUT_SIZE: usize = 10 * 1024 * 1024; // 10MB max input size

#[wasm_bindgen]
pub fn decode_entries(slot: u64, entries_data: &js_sys::Uint8Array) -> Result<Array, JsValue> {
    console_error_panic_hook::set_once();

    console_log!("Starting decode_entries for slot: {}", slot);

    // Validate input
    if slot == 0 {
        return Err(JsValue::from_str("Slot cannot be zero"));
    }

    if entries_data.length() > MAX_INPUT_SIZE as u32 {
        return Err(JsValue::from_str(&format!(
            "Input data too large: {} bytes, max allowed: {} bytes",
            entries_data.length(),
            MAX_INPUT_SIZE
        )));
    }

    if entries_data.length() == 0 {
        return Ok(Array::new());
    }

    console_log!(
        "Input validation passed, entries size: {} bytes",
        entries_data.length()
    );

    // Convert Uint8Array to Vec<u8>
    let mut data_vec = vec![0; entries_data.length() as usize];
    entries_data.copy_to(&mut data_vec);

    // Deserialize Vec<SolanaEntry> from bytes
    let entries: Vec<SolanaEntry> = bincode::deserialize(&data_vec)
        .map_err(|e| JsValue::from_str(&format!("Failed to deserialize entries: {}", e)))?;

    console_log!("Successfully deserialized {} entries", entries.len());

    let result_array = Array::new();

    for (entry_idx, entry) in entries.iter().enumerate() {
        console_log!(
            "Processing entry {}: {} transactions",
            entry_idx,
            entry.transactions.len()
        );

        for (tx_idx, transaction) in entry.transactions.iter().enumerate() {
            console_log!(
                "Transaction {} has {} signatures",
                tx_idx,
                transaction.signatures.len()
            );

            // Serialize message
            let message_bytes = bincode::serialize(&transaction.message).map_err(|e| {
                JsValue::from_str(&format!(
                    "Failed to serialize message for transaction {} in entry {}: {}",
                    tx_idx, entry_idx, e
                ))
            })?;

            let decoded_entry =
                DecodedEntry::new(slot, transaction.signatures.clone(), message_bytes);

            // Create JS object for this entry
            let js_obj = js_sys::Object::new();
            js_sys::Reflect::set(&js_obj, &"slot".into(), &JsValue::from(slot))?;

            // Convert signatures to JS array
            let sigs_array = Array::new();
            for sig in &decoded_entry.signatures {
                let sig_uint8 = js_sys::Uint8Array::new_with_length(sig.len() as u32);
                sig_uint8.copy_from(sig);
                sigs_array.push(&sig_uint8);
            }
            js_sys::Reflect::set(&js_obj, &"signatures".into(), &sigs_array)?;

            // Convert message to Uint8Array
            let msg_uint8 = js_sys::Uint8Array::new_with_length(decoded_entry.message.len() as u32);
            msg_uint8.copy_from(&decoded_entry.message);
            js_sys::Reflect::set(&js_obj, &"message".into(), &msg_uint8)?;

            result_array.push(&js_obj);
        }
    }

    console_log!(
        "Successfully decoded {} transactions total",
        result_array.length()
    );

    Ok(result_array)
}

#[wasm_bindgen]
pub fn get_version() -> String {
    env!("CARGO_PKG_VERSION").to_string()
}

#[wasm_bindgen]
pub fn test_bincode_deserialize(data: &js_sys::Uint8Array) -> Result<String, JsValue> {
    console_error_panic_hook::set_once();

    let mut data_vec = vec![0; data.length() as usize];
    data.copy_to(&mut data_vec);

    match bincode::deserialize::<Vec<SolanaEntry>>(&data_vec) {
        Ok(entries) => Ok(format!(
            "Successfully deserialized {} entries",
            entries.len()
        )),
        Err(e) => Err(JsValue::from_str(&format!("Deserialization failed: {}", e))),
    }
}
