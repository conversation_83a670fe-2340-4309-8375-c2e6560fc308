use serde::{Deserialize, Serialize};
use wasm_bindgen::prelude::*;

// Simplified Solana types for WASM compatibility
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SolanaEntry {
    pub num_hashes: u64,
    pub hash: Vec<u8>,
    pub transactions: Vec<SolanaTransaction>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SolanaTransaction {
    pub signatures: Vec<Vec<u8>>,
    pub message: SolanaMessage,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SolanaMessage {
    pub header: MessageHeader,
    pub account_keys: Vec<Vec<u8>>,
    pub recent_blockhash: Vec<u8>,
    pub instructions: Vec<CompiledInstruction>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MessageHeader {
    pub num_required_signatures: u8,
    pub num_readonly_signed_accounts: u8,
    pub num_readonly_unsigned_accounts: u8,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CompiledInstruction {
    pub program_id_index: u8,
    pub accounts: Vec<u8>,
    pub data: Vec<u8>,
}

// Output type for JavaScript
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DecodedEntry {
    pub slot: u64,
    pub signatures: Vec<Vec<u8>>,
    pub message: Vec<u8>,
}

impl DecodedEntry {
    pub fn new(slot: u64, signatures: Vec<Vec<u8>>, message: Vec<u8>) -> DecodedEntry {
        DecodedEntry {
            slot,
            signatures,
            message,
        }
    }
}

#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = console)]
    pub fn log(s: &str);
}

#[macro_export]
macro_rules! console_log {
    ($($t:tt)*) => (crate::types::log(&format_args!($($t)*).to_string()))
}
